#include "interpolationwidget.h"
#include "ui_interpolationwidget.h"
#include <QMessageBox>
#include <QDebug> // For debug output
#include "admc_api_wrapper.h" // Include the API wrapper

InterpolationWidget::InterpolationWidget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::InterpolationWidget),
    m_unitConverter(UnitConverter::getInstance()),
    m_cooldownPeriod(2000) // 设置冷却时间为2秒
{
    ui->setupUi(this);
    // Connect signals and slots for buttons
    connect(ui->setCrdPrmButton, &QPushButton::clicked, this, &InterpolationWidget::on_setCrdPrmButton_clicked);
    connect(ui->setLnPrmButton, &QPushButton::clicked, this, &InterpolationWidget::on_setLnPrmButton_clicked);
    connect(ui->setArcPrmButton, &QPushButton::clicked, this, &InterpolationWidget::on_setArcPrmButton_clicked);
    connect(ui->startCrdButton, &QPushButton::clicked, this, &InterpolationWidget::on_startCrdButton_clicked);

    // 启动计时器
    m_lastClickTimer.start();

    // 连接单位转换器的信号
    connect(m_unitConverter, &UnitConverter::unitTypeChanged, this, &InterpolationWidget::onUnitTypeChanged);

    // 创建单位切换按钮
    QGroupBox* unitGroupBox = new QGroupBox("单位设置");
    QVBoxLayout* unitLayout = new QVBoxLayout(unitGroupBox);

    QComboBox* unitComboBox = new QComboBox();
    unitComboBox->addItem("pulse");
    unitComboBox->addItem("mm");
    unitComboBox->setCurrentIndex(m_unitConverter->getCurrentUnitType());

    QPushButton* unitSwitchButton = new QPushButton("切换单位");
    connect(unitSwitchButton, &QPushButton::clicked, this, &InterpolationWidget::onUnitSwitchClicked);

    unitLayout->addWidget(new QLabel("选择单位类型："));
    unitLayout->addWidget(unitComboBox);
    unitLayout->addWidget(unitSwitchButton);

    // 将单位设置模块添加到主布局
    ui->verticalLayout_3->addWidget(unitGroupBox);

    connect(unitComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), [this, unitComboBox](int index) {
        m_unitConverter->setCurrentUnitType(static_cast<UnitType>(index));
    });

    // 设置默认参数值
    setDefaultParameters();

    // 更新单位显示
    updateUnitDisplay();
}

InterpolationWidget::~InterpolationWidget()
{
    delete ui;
}

void InterpolationWidget::on_setCrdPrmButton_clicked()
{
    // Get the singleton instance of the wrapper
    AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();

    // 0. Check if the wrapper indicates connection
    if (!wrapper->isConnected()) {
         QMessageBox::critical(this, "错误", "设备未连接。请先连接设备。");
         return;
    }

    // 1. 获取 UI 输入值
    short crd = static_cast<short>(ui->crdComboBox->currentText().toInt());
    bool okVel, okAcc, okPosX, okPosY;

    // 处理带单位的文本，提取数值部分
    QString synVelMaxText = ui->synVelMaxLineEdit->text().split(" ")[0]; // 去除可能存在的单位
    QString synAccMaxText = ui->synAccMaxLineEdit->text().split(" ")[0];
    QString originPosXText = ui->originPosXLineEdit->text().split(" ")[0];
    QString originPosYText = ui->originPosYLineEdit->text().split(" ")[0];

    double synVelMax = synVelMaxText.toDouble(&okVel);
    double synAccMax = synAccMaxText.toDouble(&okAcc);
    // 原点设置标志默认为1
    bool setOriginFlag = true;
    double originPosX = originPosXText.toDouble(&okPosX);
    double originPosY = originPosYText.toDouble(&okPosY);

    // 如果当前单位是mm，需要转换为pulse再传给API
    if (m_unitConverter->getCurrentUnitType() == UNIT_MM) {
        // 位置需要转换，因为1mm = 1000pulse
        originPosX = m_unitConverter->mmToPulse(originPosX);
        originPosY = m_unitConverter->mmToPulse(originPosY);
        // 速度保持不变，因为1mm/s = 1pulse/ms
        // 加速度需要转换，因为1mm/s^2 = 0.001pulse/ms^2
        synAccMax = m_unitConverter->mmPerSSquaredToPulsePerMsSquared(synAccMax);
    }

    // Basic input validation
    if (!okVel || !okAcc || !okPosX || !okPosY) {
        QMessageBox::warning(this, "输入错误", "速度、加速度或原点位置包含无效数值。");
        return;
    }

    // 2. 准备参数 - 新接口直接传递参数
    // 注意：新接口只支持synVelMax和synAccMax参数，原点设置等功能可能需要其他API

    // 3. 通过 AdmcApiWrapper 调用 setCrdPrm 函数
    qDebug() << "Calling AdmcApiWrapper::setCrdPrm for crd:" << crd;
    qDebug() << " synVelMax:" << synVelMax
             << " synAccMax:" << synAccMax
             << " 注意：新接口不支持原点设置，setOriginFlag和originPos参数被忽略";

    short result = wrapper->setCrdPrm(crd, synVelMax, synAccMax);

    // 4. 检查返回值并提示用户
    if (result == 0) { // Assuming 0 means success
        emit apiStatusChanged(QString("坐标系 %1 参数设置成功！").arg(crd), true);
    } else {
        // Use the wrapper's error string function
        QMessageBox::warning(this, "失败", QString("坐标系 %1 参数设置失败: %2").arg(crd).arg(wrapper->getErrorString(result)));
        emit apiStatusChanged(QString("坐标系 %1 参数设置失败: %2").arg(crd).arg(wrapper->getErrorString(result)), false);
    }
}

void InterpolationWidget::on_setArcPrmButton_clicked()
{
    // Get the singleton instance of the wrapper
    AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();

    // 0. Check if the wrapper indicates connection
    if (!wrapper->isConnected()) {
         QMessageBox::critical(this, "错误", "设备未连接。请先连接设备。");
         return;
    }

    // 1. 获取 UI 输入值
    // short crd = static_cast<short>(ui->crdComboBox->currentText().toInt()); // 从坐标系参数设置组获取 crd
    bool okX, okY, okRadius, okVel, okAcc;

    // 处理带单位的文本，提取数值部分
    QString xText = ui->arc_xLineEdit->text().split(" ")[0];
    QString yText = ui->arc_yLineEdit->text().split(" ")[0];
    QString radiusText = ui->arc_radiusLineEdit->text().split(" ")[0];
    QString synVelText = ui->arc_synVelLineEdit->text().split(" ")[0];
    QString synAccText = ui->arc_synAccLineEdit->text().split(" ")[0];

    int32_t x = xText.toInt(&okX);
    int32_t y = yText.toInt(&okY);
    double radius = radiusText.toDouble(&okRadius);
    // short circleDir = static_cast<short>(ui->arc_circleDirSpinBox->value()); // 暂时未使用
    double synVel = synVelText.toDouble(&okVel);
    double synAcc = synAccText.toDouble(&okAcc);
    // 末速度默认为0
    // double velEnd = 0.0; // 暂时未使用

    // 如果当前单位是mm，需要转换为pulse再传给API
    if (m_unitConverter->getCurrentUnitType() == UNIT_MM) {
        // 位置需要转换，因为1mm = 1000pulse
        x = static_cast<int32_t>(m_unitConverter->mmToPulse(x));
        y = static_cast<int32_t>(m_unitConverter->mmToPulse(y));
        radius = m_unitConverter->mmToPulse(radius);
        // 速度保持不变，因为1mm/s = 1pulse/ms
        // 加速度需要转换，因为1mm/s^2 = 0.001pulse/ms^2
        synAcc = m_unitConverter->mmPerSSquaredToPulsePerMsSquared(synAcc);
    }

    // Basic input validation
    if (!okX || !okY || !okRadius || !okVel || !okAcc) {
        QMessageBox::warning(this, "输入错误", "圆弧插补参数包含无效数值。请检查 X, Y, 半径, 速度和加速度。");
        return;
    }

    QMessageBox::information(this, "功能未实现", "圆弧插补参数设置功能尚未实现。");
}

void InterpolationWidget::on_startCrdButton_clicked()
{
    // 检查是否在冷却时间内
    qint64 elapsedMs = m_lastClickTimer.elapsed();
    if (elapsedMs < m_cooldownPeriod) {
        qDebug() << "按钮点击冷却中，还需等待" << (m_cooldownPeriod - elapsedMs) << "毫秒，忽略此次点击";
        return;
    }

    // 获取API包装器实例
    AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();

    // 检查设备连接状态
    if (!wrapper->isConnected()) {
        QMessageBox::critical(this, "错误", "设备未连接。请先连接设备。");
        return;
    }

    // 获取当前选中的坐标系
    short crd = static_cast<short>(ui->crdComboBox->currentText().toInt());

    // 调用API启动坐标系插补运动
    qDebug() << "Calling AdmcApiWrapper::crdStart for crd:" << crd;
    short result = wrapper->crdStart(crd);

    // 检查返回值并提示用户
    if (result == 0) {
        emit apiStatusChanged(QString("坐标系 %1 插补运动已启动！").arg(crd), true);

        // 操作成功，重置计时器开始冷却时间
        m_lastClickTimer.restart();

        // 临时禁用按钮，提供视觉反馈
        ui->startCrdButton->setEnabled(false);
        QTimer::singleShot(500, this, [this]() {
            ui->startCrdButton->setEnabled(true);
        });
    } else {
        QMessageBox::warning(this, "失败", QString("启动坐标系 %1 插补运动失败: %2").arg(crd).arg(wrapper->getErrorString(result)));
        emit apiStatusChanged(QString("启动坐标系 %1 插补运动失败: %2").arg(crd).arg(wrapper->getErrorString(result)), false);
    }
}

void InterpolationWidget::on_setLnPrmButton_clicked()
{
    // Get the singleton instance of the wrapper
    AdmcApiWrapper* wrapper = AdmcApiWrapper::getInstance();

    // 0. Check if the wrapper indicates connection
    if (!wrapper->isConnected()) {
         QMessageBox::critical(this, "错误", "设备未连接。请先连接设备。");
         return;
    }

    // 1. 获取 UI 输入值
    short crd = static_cast<short>(ui->crdComboBox->currentText().toInt()); // 从坐标系参数设置组获取 crd
    bool okX, okY, okVel, okAcc;

    // 处理带单位的文本，提取数值部分
    QString xText = ui->ln_xLineEdit->text().split(" ")[0];
    QString yText = ui->ln_yLineEdit->text().split(" ")[0];
    QString synVelText = ui->ln_synVelLineEdit->text().split(" ")[0];
    QString synAccText = ui->ln_synAccLineEdit->text().split(" ")[0];

    int32_t x = xText.toInt(&okX);
    int32_t y = yText.toInt(&okY);
    double synVel = synVelText.toDouble(&okVel);
    double synAcc = synAccText.toDouble(&okAcc);
    // 末速度默认为0
    double velEnd = 0.0; // 这个变量在后面的API调用中使用

    // 如果当前单位是mm，需要转换为pulse再传给API
    if (m_unitConverter->getCurrentUnitType() == UNIT_MM) {
        // 位置需要转换，因为1mm = 1000pulse
        x = static_cast<int32_t>(m_unitConverter->mmToPulse(x));
        y = static_cast<int32_t>(m_unitConverter->mmToPulse(y));
        // 速度保持不变，因为1mm/s = 1pulse/ms
        // 加速度需要转换，因为1mm/s^2 = 0.001pulse/ms^2
        synAcc = m_unitConverter->mmPerSSquaredToPulsePerMsSquared(synAcc);
    }

    // Basic input validation
    if (!okX || !okY || !okVel || !okAcc) {
        QMessageBox::warning(this, "输入错误", "直线插补参数包含无效数值。请检查 X, Y, 速度和加速度。");
        return;
    }

    // 2. 调用 AdmcApiWrapper::ln 函数
    qDebug() << "Calling AdmcApiWrapper::ln for crd:" << crd << " x:" << x << " y:" << y
             << " synVel:" << synVel << " synAcc:" << synAcc << " velEnd:" << velEnd;

    short result = wrapper->ln(crd, x, y, synVel, synAcc, velEnd);

    // 3. 检查返回值并提示用户
    if (result == 0) {
        emit apiStatusChanged(QString("坐标系 %1 直线插补参数设置成功！").arg(crd), true);
    } else {
        // Use the wrapper's error string function
        QMessageBox::warning(this, "失败", QString("坐标系 %1 直线插补参数设置失败: %2").arg(crd).arg(wrapper->getErrorString(result)));
        emit apiStatusChanged(QString("坐标系 %1 直线插补参数设置失败: %2").arg(crd).arg(wrapper->getErrorString(result)), false);
    }
}

void InterpolationWidget::on_setButton_clicked()
{
    // TODO: Implement the functionality for setButton clicked
    QMessageBox::information(this, "功能未实现", "Set Button 功能尚未实现。");
}

void InterpolationWidget::updateUnitDisplay()
{
    // 清除旧的单位标签
    // 查找并删除所有带有"unitLabel"对象名的标签
    QList<QLabel*> oldLabels = findChildren<QLabel*>(QRegExp(".*unitLabel.*"));
    for (QLabel* label : oldLabels) {
        label->deleteLater();
    }

    // 获取单位字符串
    QString velUnit = m_unitConverter->getVelocityUnitString();
    QString accUnit = m_unitConverter->getAccelerationUnitString();
    QString posUnit = m_unitConverter->getPositionUnitString();

    // 更新坐标系参数设置的单位显示
    ui->synVelMaxLabel->setText("同步最大速度:");
    ui->synAccMaxLabel->setText("同步最大加速度:");
    ui->originPosLabel->setText("原点位置 (X, Y):");

    // 设置文本框的最小宽度以保持对齐
    ui->synVelMaxLineEdit->setMinimumWidth(120);
    ui->synAccMaxLineEdit->setMinimumWidth(120);
    ui->originPosXLineEdit->setMinimumWidth(120);
    ui->originPosYLineEdit->setMinimumWidth(120);

    // 更新直线插补参数设置的单位显示
    ui->ln_xLabel->setText("X:");
    ui->ln_yLabel->setText("Y:");
    ui->ln_synVelLabel->setText("同步速度:");
    ui->ln_synAccLabel->setText("同步加速度:");

    // 设置文本框的最小宽度以保持对齐
    ui->ln_xLineEdit->setMinimumWidth(120);
    ui->ln_yLineEdit->setMinimumWidth(120);
    ui->ln_synVelLineEdit->setMinimumWidth(120);
    ui->ln_synAccLineEdit->setMinimumWidth(120);

    // 更新圆弧插补参数设置的单位显示
    ui->arc_xLabel->setText("X:");
    ui->arc_yLabel->setText("Y:");
    ui->arc_radiusLabel->setText("半径 (R):");
    ui->arc_synVelLabel->setText("同步速度:");
    ui->arc_synAccLabel->setText("同步加速度:");

    // 设置文本框的最小宽度以保持对齐
    ui->arc_xLineEdit->setMinimumWidth(120);
    ui->arc_yLineEdit->setMinimumWidth(120);
    ui->arc_radiusLineEdit->setMinimumWidth(120);
    ui->arc_synVelLineEdit->setMinimumWidth(120);
    ui->arc_synAccLineEdit->setMinimumWidth(120);

    // 为坐标系参数设置的输入框添加单位标签
    // 获取当前文本并处理单位
    QString synVelMaxText = ui->synVelMaxLineEdit->text();
    synVelMaxText = synVelMaxText.split(" ")[0]; // 去除可能存在的单位
    ui->synVelMaxLineEdit->setText(synVelMaxText + " " + velUnit);

    QString synAccMaxText = ui->synAccMaxLineEdit->text();
    synAccMaxText = synAccMaxText.split(" ")[0];
    ui->synAccMaxLineEdit->setText(synAccMaxText + " " + accUnit);

    QString originPosXText = ui->originPosXLineEdit->text();
    originPosXText = originPosXText.split(" ")[0];
    ui->originPosXLineEdit->setText(originPosXText + " " + posUnit);

    QString originPosYText = ui->originPosYLineEdit->text();
    originPosYText = originPosYText.split(" ")[0];
    ui->originPosYLineEdit->setText(originPosYText + " " + posUnit);

    // 为直线插补参数输入框添加单位标签
    QString ln_xText = ui->ln_xLineEdit->text();
    ln_xText = ln_xText.split(" ")[0];
    ui->ln_xLineEdit->setText(ln_xText + " " + posUnit);

    QString ln_yText = ui->ln_yLineEdit->text();
    ln_yText = ln_yText.split(" ")[0];
    ui->ln_yLineEdit->setText(ln_yText + " " + posUnit);

    QString ln_synVelText = ui->ln_synVelLineEdit->text();
    ln_synVelText = ln_synVelText.split(" ")[0];
    ui->ln_synVelLineEdit->setText(ln_synVelText + " " + velUnit);

    QString ln_synAccText = ui->ln_synAccLineEdit->text();
    ln_synAccText = ln_synAccText.split(" ")[0];
    ui->ln_synAccLineEdit->setText(ln_synAccText + " " + accUnit);

    // 为圆弧插补参数输入框添加单位标签
    QString arc_xText = ui->arc_xLineEdit->text();
    arc_xText = arc_xText.split(" ")[0];
    ui->arc_xLineEdit->setText(arc_xText + " " + posUnit);

    QString arc_yText = ui->arc_yLineEdit->text();
    arc_yText = arc_yText.split(" ")[0];
    ui->arc_yLineEdit->setText(arc_yText + " " + posUnit);

    QString arc_radiusText = ui->arc_radiusLineEdit->text();
    arc_radiusText = arc_radiusText.split(" ")[0];
    ui->arc_radiusLineEdit->setText(arc_radiusText + " " + posUnit);

    QString arc_synVelText = ui->arc_synVelLineEdit->text();
    arc_synVelText = arc_synVelText.split(" ")[0];
    ui->arc_synVelLineEdit->setText(arc_synVelText + " " + velUnit);

    QString arc_synAccText = ui->arc_synAccLineEdit->text();
    arc_synAccText = arc_synAccText.split(" ")[0];
    ui->arc_synAccLineEdit->setText(arc_synAccText + " " + accUnit);
}

void InterpolationWidget::setDefaultParameters()
{
    // 设置坐标系参数的默认值
    if (m_unitConverter->getCurrentUnitType() == UNIT_PULSE) {
        // pulse单位下的默认值
        ui->synVelMaxLineEdit->setText("2000");    // 2000 pulse/ms
        ui->synAccMaxLineEdit->setText("20");      // 20 pulse/ms^2
        ui->originPosXLineEdit->setText("0");      // 0 pulse
        ui->originPosYLineEdit->setText("0");      // 0 pulse
    } else {
        // mm单位下的默认值
        ui->synVelMaxLineEdit->setText("2000");    // 2000 mm/s
        ui->synAccMaxLineEdit->setText("20000");   // 20000 mm/s^2
        ui->originPosXLineEdit->setText("0");      // 0 mm
        ui->originPosYLineEdit->setText("0");      // 0 mm
    }

    // 设置直线插补参数的默认值
    if (m_unitConverter->getCurrentUnitType() == UNIT_PULSE) {
        // pulse单位下的默认值
        ui->ln_xLineEdit->setText("0");           // 0 pulse
        ui->ln_yLineEdit->setText("0");           // 0 pulse
        ui->ln_synVelLineEdit->setText("100");     // 100 pulse/ms
        ui->ln_synAccLineEdit->setText("10");      // 10 pulse/ms^2
    } else {
        // mm单位下的默认值
        ui->ln_xLineEdit->setText("0");           // 0 mm
        ui->ln_yLineEdit->setText("0");           // 0 mm
        ui->ln_synVelLineEdit->setText("100");     // 100 mm/s
        ui->ln_synAccLineEdit->setText("10000");   // 10000 mm/s^2
    }

    // 设置圆弧插补参数的默认值
    if (m_unitConverter->getCurrentUnitType() == UNIT_PULSE) {
        // pulse单位下的默认值
        ui->arc_xLineEdit->setText("0");          // 0 pulse
        ui->arc_yLineEdit->setText("0");          // 0 pulse
        ui->arc_radiusLineEdit->setText("0");      // 0 pulse
        ui->arc_synVelLineEdit->setText("100");    // 100 pulse/ms
        ui->arc_synAccLineEdit->setText("10");     // 10 pulse/ms^2
    } else {
        // mm单位下的默认值
        ui->arc_xLineEdit->setText("0");          // 0 mm
        ui->arc_yLineEdit->setText("0");          // 0 mm
        ui->arc_radiusLineEdit->setText("0");      // 0 mm
        ui->arc_synVelLineEdit->setText("100");    // 100 mm/s
        ui->arc_synAccLineEdit->setText("10000");  // 10000 mm/s^2
    }

    // 设置完默认参数后更新单位显示
    updateUnitDisplay();
}

void InterpolationWidget::onUnitTypeChanged(UnitType type)
{
    // 转换坐标系参数的值
    bool /*okVelMax,*/ okAccMax, okPosX, okPosY;
    // 获取文本并处理单位
    QString synAccMaxText = ui->synAccMaxLineEdit->text().split(" ")[0];
    QString originPosXText = ui->originPosXLineEdit->text().split(" ")[0];
    QString originPosYText = ui->originPosYLineEdit->text().split(" ")[0];

    double synAccMax = synAccMaxText.toDouble(&okAccMax);
    double originPosX = originPosXText.toDouble(&okPosX);
    double originPosY = originPosYText.toDouble(&okPosY);

    if (type == UNIT_PULSE) {
        // 从 mm 转换到 pulse
        if (okPosX) ui->originPosXLineEdit->setText(QString::number(originPosX * 1000)); // 从 mm 转换到 pulse
        if (okPosY) ui->originPosYLineEdit->setText(QString::number(originPosY * 1000)); // 从 mm 转换到 pulse
        // 速度保持不变
        // 加速度需要转换
        if (okAccMax) ui->synAccMaxLineEdit->setText(QString::number(synAccMax * 0.001)); // 从 mm/s^2 转换到 pulse/ms^2
    } else {
        // 从 pulse 转换到 mm
        if (okPosX) ui->originPosXLineEdit->setText(QString::number(originPosX * 0.001)); // 从 pulse 转换到 mm
        if (okPosY) ui->originPosYLineEdit->setText(QString::number(originPosY * 0.001)); // 从 pulse 转换到 mm
        // 速度保持不变
        // 加速度需要转换
        if (okAccMax) ui->synAccMaxLineEdit->setText(QString::number(synAccMax * 1000)); // 从 pulse/ms^2 转换到 mm/s^2
    }

    // 转换直线插补参数的值
    bool okLnX, okLnY, /*okLnVel,*/ okLnAcc;
    QString lnXText = ui->ln_xLineEdit->text().split(" ")[0];
    QString lnYText = ui->ln_yLineEdit->text().split(" ")[0];
    QString lnAccText = ui->ln_synAccLineEdit->text().split(" ")[0];

    double lnX = lnXText.toDouble(&okLnX);
    double lnY = lnYText.toDouble(&okLnY);
    double lnAcc = lnAccText.toDouble(&okLnAcc);

    if (type == UNIT_PULSE) {
        // 从 mm 转换到 pulse
        if (okLnX) ui->ln_xLineEdit->setText(QString::number(static_cast<int>(lnX * 1000))); // 从 mm 转换到 pulse
        if (okLnY) ui->ln_yLineEdit->setText(QString::number(static_cast<int>(lnY * 1000))); // 从 mm 转换到 pulse
        // 速度保持不变
        // 加速度需要转换
        if (okLnAcc) ui->ln_synAccLineEdit->setText(QString::number(lnAcc * 0.001)); // 从 mm/s^2 转换到 pulse/ms^2
    } else {
        // 从 pulse 转换到 mm
        if (okLnX) ui->ln_xLineEdit->setText(QString::number(lnX * 0.001, 'f', 3)); // 从 pulse 转换到 mm
        if (okLnY) ui->ln_yLineEdit->setText(QString::number(lnY * 0.001, 'f', 3)); // 从 pulse 转换到 mm
        // 速度保持不变
        // 加速度需要转换
        if (okLnAcc) ui->ln_synAccLineEdit->setText(QString::number(lnAcc * 1000)); // 从 pulse/ms^2 转换到 mm/s^2
    }

    // 转换圆弧插补参数的值
    bool okArcX, okArcY, okArcRadius, /*okArcVel,*/ okArcAcc;
    QString arcXText = ui->arc_xLineEdit->text().split(" ")[0];
    QString arcYText = ui->arc_yLineEdit->text().split(" ")[0];
    QString arcRadiusText = ui->arc_radiusLineEdit->text().split(" ")[0];
    QString arcAccText = ui->arc_synAccLineEdit->text().split(" ")[0];

    double arcX = arcXText.toDouble(&okArcX);
    double arcY = arcYText.toDouble(&okArcY);
    double arcRadius = arcRadiusText.toDouble(&okArcRadius);
    double arcAcc = arcAccText.toDouble(&okArcAcc);

    if (type == UNIT_PULSE) {
        // 从 mm 转换到 pulse
        if (okArcX) ui->arc_xLineEdit->setText(QString::number(static_cast<int>(arcX * 1000))); // 从 mm 转换到 pulse
        if (okArcY) ui->arc_yLineEdit->setText(QString::number(static_cast<int>(arcY * 1000))); // 从 mm 转换到 pulse
        if (okArcRadius) ui->arc_radiusLineEdit->setText(QString::number(arcRadius * 1000)); // 从 mm 转换到 pulse
        // 速度保持不变
        // 加速度需要转换
        if (okArcAcc) ui->arc_synAccLineEdit->setText(QString::number(arcAcc * 0.001)); // 从 mm/s^2 转换到 pulse/ms^2
    } else {
        // 从 pulse 转换到 mm
        if (okArcX) ui->arc_xLineEdit->setText(QString::number(arcX * 0.001, 'f', 3)); // 从 pulse 转换到 mm
        if (okArcY) ui->arc_yLineEdit->setText(QString::number(arcY * 0.001, 'f', 3)); // 从 pulse 转换到 mm
        if (okArcRadius) ui->arc_radiusLineEdit->setText(QString::number(arcRadius * 0.001, 'f', 3)); // 从 pulse 转换到 mm
        // 速度保持不变
        // 加速度需要转换
        if (okArcAcc) ui->arc_synAccLineEdit->setText(QString::number(arcAcc * 1000)); // 从 pulse/ms^2 转换到 mm/s^2
    }

    // 在所有转换完成后更新单位显示
    updateUnitDisplay();
}

void InterpolationWidget::onUnitSwitchClicked()
{
    // 切换单位类型
    UnitType currentType = m_unitConverter->getCurrentUnitType();
    UnitType newType = (currentType == UNIT_PULSE) ? UNIT_MM : UNIT_PULSE;

    // 设置新的单位类型
    m_unitConverter->setCurrentUnitType(newType);
}
